export const winbackTemplate = {
    name: 'Inquiry Winback',
    description: 'A workflow to win back guests who have made an inquiry but did not book.',
    type: 'winback',
    trigger: 'inquiry',
    listings: [],
    steps: {
        1: { type: 'delay', duration: '1d' },
        2: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, are you sure you do not want to book?'
        },
        3: {
            type: 'delay',
            duration: '1d'
        },
        4: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book.'
        },
        5: {
            type: 'delay',
            duration: '1d'
        },
        6: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book. If you have any questions, please let us know.'
        },
        7: {
            type: 'delay',
            duration: '1d'
        },
        8: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book.'
        }
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true
}


export const earlyCheckInUpsellTemplate = {
    name: 'Early Check-In Upsell',
    description: 'A workflow to upsell early check-in to guests who have booked.',
    type: 'upsell',
    trigger: 'checkin',
    triggerTiming: {
        time: '5d',
        when: 'before'
    },
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'Early Check-In Available',
            body: 'Hi, we have early check-in available for your stay. Would you like to add it?'
        },
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true

}

export const preStayGapNightUpsellTemplate = {
    name: 'Pre-Stay Gap Night Upsell',
    description: 'A workflow to upsell a pre-stay night to guests who have booked.',
    type: 'upsell',
    trigger: 'checkin',
    triggerTiming: {
        time: '5d',
        when: 'before'
    },
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'Pre-Stay Night Available',
            body: 'Hi, we have a pre-stay night available for your stay. Would you like to add it?'
        },
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true
}

export const lateCheckOutUpsellTemplate = {
    name: 'Late Check-Out Upsell',
    description: 'A workflow to upsell late check-out to guests who have booked.',
    type: 'upsell',
    trigger: 'checkout',
    triggerTiming: {
        time: '5d',
        when: 'before'
    },
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'Late Check-Out Available',
            body: 'Hi, we have late check-out available for your stay. Would you like to add it?'
        },
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true
}

export const postStayExtraNightUpsellTemplate = {
    name: 'Post-Stay Extra Night Upsell',
    description: 'A workflow to upsell a post-stay night to guests who have booked.',
    type: 'upsell',
    trigger: 'checkout',
    triggerTiming: {
        time: '5d',
        when: 'before'
    },
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'Post-Stay Night Available',
            body: 'Hi, we have a post-stay night available for your stay. Would you like to add it?'
        },
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true
}