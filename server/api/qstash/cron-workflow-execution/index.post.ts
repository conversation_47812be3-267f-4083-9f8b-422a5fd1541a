import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {

  const midnightUTC = new Date(Date.now());
  midnightUTC.setUTCHours(0, 0, 0, 0);
  const midnightTimestamp = midnightUTC.getTime();

  console.log('midnight timestamp: ', midnightTimestamp)

  const workflowsSnap = await firestore.collection('workflows').where('active', '==', true).get()
  const workflows = workflowsSnap.docs.map(d => ({ ...d.data(), id: d.id }))

  console.log('workflows: ', workflows)

  return 'Hello Nitro'
})
